<script setup lang="ts">
import { format } from 'date-fns'
import { onMounted, ref } from 'vue'
import CapacityChart from './components/CapacityChart.vue'
import { capacityApi } from '~/api/capacity'
import type { CapacityReportWithLine } from '~/api/capacity/type'
import PageContainer from '~/components/common/PageContainer.vue'

// 响应式数据
const selectedDate = ref<Date>(new Date())
const loading = ref(false)

const lineData = ref<CapacityReportWithLine[]>()

// 刷新数据
async function refreshData() {
  loading.value = true
  try {
    const dateStr = format(selectedDate.value, 'yyyy-MM-dd')
    // 调用实际的API
    const res = await capacityApi.getReport(dateStr)

    lineData.value = res
  }
  catch (error) {
    console.error('获取数据失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  refreshData()
})
</script>

<template>
  <PageContainer>
    <!-- 页面标题 -->
    <div class="mb-6 text-center">
      <h1 class="text-2xl text-primary font-bold">
        多线产能看板
      </h1>
      <!-- <p class="mt-2 text-muted-color">
        Multi-Line Capacity Dashboard
      </p> -->
    </div>

    <!-- 筛选器 -->
    <div class="mb-6 flex items-center justify-center gap-4 border border-surface-border rounded-lg bg-surface-card p-4">
      <label class="font-medium">日期:</label>
      <DatePicker
        v-model="selectedDate"
        date-format="yy-mm-dd"
        show-icon
        fluid
        class="w-48"
      />
      <Button
        label="刷新全部"
        icon="pi pi-refresh"
        :loading="loading"
        @click="refreshData"
      />
    </div>

    <!-- 产线报表容器 -->
    <div class="space-y-8">
      <div
        v-for="(index, data) in lineData"
        :key="data"
        class="border border-surface-border rounded-lg bg-surface-card p-6 shadow-sm"
      >
        <!-- 产线标题 -->
        <h2 class="mb-6 border-b border-surface-border pb-3 text-xl text-primary font-semibold">
          产线 (Line): {{ lineId }}
        </h2>

        <!-- 图表 -->
        <div class="mb-6 h-80">
          <CapacityChart
            :line-id="lineId"
            :data="data"
          />
        </div>

        <!-- 数据表格 -->
        <DataTable
          :value="data"
          class="mt-4"
          striped-rows
          responsive-layout="scroll"
        >
          <Column field="hour" header="小时 (Hour)" class="text-center">
            <template #body="{ data }">
              {{ data.hour }}:00
            </template>
          </Column>
          <Column field="plannedQuantity" header="计划产能 (Planned)" class="text-center" />
          <Column field="actualQuantity" header="实际产能 (Actual)" class="text-center" />
          <Column field="achievementRate" header="达成率 (Rate)" class="text-center" />
          <Column field="status" header="状态 (Status)" class="text-center">
            <template #body="{ data }">
              <Tag
                :value="data.status"
                :severity="data.status === '达标' ? 'success' : 'danger'"
              />
            </template>
          </Column>
          <Column field="reason" header="未达成原因 (Reason)" class="text-center">
            <template #body="{ data }">
              {{ data.reason || 'N/A' }}
            </template>
          </Column>
        </DataTable>
      </div>
    </div>
  </PageContainer>
</template>

<style scoped>
.space-y-8 > * + * {
  margin-top: 2rem;
}
</style>
