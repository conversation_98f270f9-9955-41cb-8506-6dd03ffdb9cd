# 多线产能看板 Vue 组件

## 概述

基于原始HTML文件创建的Vue组件，使用PrimeVue组件库和ECharts图表库实现多线产能看板功能。

## 功能特性

- ✅ 响应式设计，支持明暗主题切换
- ✅ 日期选择器，可选择查看特定日期的数据
- ✅ 多产线数据展示，每个产线包含：
  - 交互式折线图（计划产能 vs 实际产能）
  - 详细数据表格
  - 状态标签（达标/未达标）
- ✅ 数据刷新功能
- ✅ API集成支持

## 文件结构

```
src/pages/capacity/
├── MultiLineCapacityDashboard.vue  # 主组件
├── components/
│   └── CapacityChart.vue           # 图表组件
└── index.vue                       # 入口页面

src/api/capacity/
├── index.ts                        # API接口
└── types.ts                        # 类型定义
```

## 组件使用

### 1. 路由配置

已在 `src/router/admin.ts` 中添加路由：

```typescript
{
  path: '/capacity/multi-line-dashboard',
  name: 'multi-line-capacity-dashboard',
  component: () => import('~/pages/capacity/MultiLineCapacityDashboard.vue'),
  meta: {},
}
```

### 2. 直接使用组件

```vue
<template>
  <MultiLineCapacityDashboard />
</template>

<script setup>
import MultiLineCapacityDashboard from '~/pages/capacity/MultiLineCapacityDashboard.vue'
</script>
```

## API接口

### 数据类型

```typescript
interface HourlyCapacityData {
  hour: number
  plannedQuantity: number
  actualQuantity: number
  achievementRate: string
  status: '达标' | '未达标'
  reason: string | null
}

interface LineCapacityData {
  lineId: string
  lineName: string
  data: HourlyCapacityData[]
}

interface MultiLineCapacityData {
  date: string
  lines: LineCapacityData[]
}
```

### API方法

```typescript
// 获取多线产能数据
capacityApi.getMultiLineCapacityData({ date: '2025-07-15' })

// 获取多线产能统计
capacityApi.getMultiLineCapacityStatistics({ date: '2025-07-15' })

// 获取可用产线列表
capacityApi.getAvailableLines()
```

## 样式特性

- 使用PrimeVue主题系统，自动适配明暗模式
- 响应式布局，适配不同屏幕尺寸
- 图表使用渐变色和动画效果
- 表格支持条纹行和响应式滚动

## 自定义配置

### 图表配置

可以通过修改 `CapacityChart.vue` 中的 `chartOption` 来自定义图表样式：

- 颜色主题
- 动画效果
- 图表类型
- 坐标轴配置

### 数据格式

组件支持灵活的数据格式，如果后端API返回的数据格式不同，可以在 `refreshData` 函数中进行数据转换。

## 开发建议

1. **测试**: 建议编写单元测试来验证组件功能
2. **错误处理**: 可以添加更详细的错误处理和用户提示
3. **性能优化**: 对于大量数据，可以考虑虚拟滚动或分页
4. **国际化**: 可以添加i18n支持多语言

## 访问地址

开发环境访问地址：`/capacity/multi-line-dashboard`
